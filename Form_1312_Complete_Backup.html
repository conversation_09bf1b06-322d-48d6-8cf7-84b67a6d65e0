<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>טופס 1312א - בקשה להנחה במס הכנסה - מערכת מלאה</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
            direction: rtl;
        }

        .print-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 12px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
            transition: all 0.3s ease;
        }

        .print-btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,123,255,0.4);
        }

        .form-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1000px;
            margin: 0 auto;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
        }

        .header-text {
            text-align: right;
        }

        .header-title {
            font-size: 2rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .form-number {
            background: rgba(255,255,255,0.2);
            padding: 15px 25px;
            border-radius: 10px;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .form-content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .section-title {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px 25px;
            font-size: 1.3rem;
            font-weight: 600;
            color: #495057;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-title i {
            color: #007bff;
            font-size: 1.4rem;
        }

        .section-content {
            padding: 30px 25px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            flex: 1;
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .required {
            color: #dc3545;
            margin-right: 4px;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        .form-input.invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 3px rgba(220,53,69,0.1);
        }

        .validation-message {
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
        }

        .checkbox-input {
            width: 18px;
            height: 18px;
            accent-color: #007bff;
        }

        .checkbox-label {
            font-size: 14px;
            color: #495057;
            cursor: pointer;
        }

        .radio-group {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .radio-input {
            width: 16px;
            height: 16px;
            accent-color: #007bff;
        }

        .radio-label {
            font-size: 14px;
            color: #495057;
            cursor: pointer;
        }

        .info-box {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .info-title {
            font-weight: 600;
            color: #0c5460;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .authority-form {
            background: #fff8e1;
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .autocomplete-container {
            position: relative;
        }

        .autocomplete-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ffc107;
            border-radius: 8px;
            font-size: 14px;
            background: #fff;
        }

        .autocomplete-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .autocomplete-item {
            padding: 12px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
            transition: background-color 0.2s;
        }

        .autocomplete-item:hover,
        .autocomplete-item.selected {
            background: #f8f9fa;
        }

        .autocomplete-item:last-child {
            border-bottom: none;
        }

        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }

        .children-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .children-table th,
        .children-table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #e9ecef;
        }

        .children-table th {
            font-weight: bold;
            font-size: 14px;
        }

        .children-table input,
        .children-table select {
            width: 100%;
            min-width: 100px;
            border: 1px solid #ddd;
            padding: 8px;
            border-radius: 4px;
            font-size: 13px;
        }

        /* Children table styles */
        .children-table {
            font-size: 14px;
        }
        
        .children-table input {
            font-size: 13px;
        }

        /* OCR and Document Upload Styles */
        .document-upload-section {
            transition: all 0.3s ease;
        }

        .document-upload-section:hover {
            border-color: #0056b3;
            background: #f0f8ff;
        }

        .document-item {
            transition: all 0.3s ease;
        }

        .document-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-outline-primary {
            color: #007bff;
            border: 1px solid #007bff;
            background: white;
        }

        .btn-outline-primary:hover {
            background: #007bff;
            color: white;
        }

        .btn-outline-info {
            color: #17a2b8;
            border: 1px solid #17a2b8;
            background: white;
        }

        .btn-outline-info:hover {
            background: #17a2b8;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
            border: 1px solid #28a745;
        }

        .btn-success:hover {
            background: #218838;
            border-color: #1e7e34;
        }

        .btn-outline-pink {
            color: #e83e8c;
            border: 1px solid #e83e8c;
            background: white;
        }

        .btn-outline-pink:hover {
            background: #e83e8c;
            color: white;
        }

        .btn-outline-warning {
            color: #ffc107;
            border: 1px solid #ffc107;
            background: white;
        }

        .btn-outline-warning:hover {
            background: #ffc107;
            color: #212529;
        }

        .btn-outline-danger {
            color: #dc3545;
            border: 1px solid #dc3545;
            background: white;
        }

        .btn-outline-danger:hover {
            background: #dc3545;
            color: white;
        }

        .loading-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .ocr-result {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Form Type Selection Styles */
        .form-option:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        }

        .scan-upload-area:hover {
            border-color: #007bff !important;
            background: #f0f8ff !important;
        }

        @media (max-width: 1200px) {
            .table-container {
                font-size: 12px;
            }
            
            .children-table th,
            .children-table td {
                padding: 8px 5px !important;
            }
            
            .children-table input {
                font-size: 12px;
                padding: 6px !important;
                min-width: 80px !important;
            }
        }

        @media (max-width: 768px) {
            .authority-form {
                grid-template-columns: 1fr;
            }
            
            .table-container {
                font-size: 11px;
            }
            
            .children-table th,
            .children-table td {
                padding: 6px 3px !important;
            }
            
            .children-table input {
                font-size: 11px;
                padding: 4px !important;
                min-width: 60px !important;
            }
            
            .children-table th {
                font-size: 10px;
            }

            .form-type-selection div[style*="grid-template-columns"] {
                grid-template-columns: 1fr !important;
            }
            
            .form-option {
                margin-bottom: 15px;
            }

            .form-container {
                margin: 10px;
                padding: 15px;
            }
            
            .form-row {
                flex-direction: column;
            }
            
            .form-group {
                margin-bottom: 15px;
            }
            
            .header-title {
                font-size: 1.5rem;
            }
            
            .section-title {
                font-size: 1.1rem;
            }
        }

        .signature-section {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 25px;
            margin-top: 30px;
        }

        .signature-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .signature-box {
            text-align: center;
            padding: 20px;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .signature-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 12px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-2px);
        }

        @media print {
            .print-btn,
            .form-actions {
                display: none;
            }
            
            body {
                background: white;
                padding: 0;
            }
            
            .form-container {
                box-shadow: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <button class="btn btn-secondary print-btn" onclick="window.print()">
        <i class="fas fa-print"></i> הדפס
    </button>

    <!-- Form Type Selection -->
    <div class="form-type-selection" id="formTypeSelection" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 15px; margin: 20px; text-align: center; color: white;">
        <h2 style="margin-bottom: 20px; font-size: 24px;">
            <i class="fas fa-file-alt" style="margin-left: 10px;"></i>
            בחר את דרך המילוי המועדפת עליך
        </h2>
        <p style="margin-bottom: 30px; font-size: 16px; opacity: 0.9;">
            ניתן למלא את הטופס דיגיטלית או לסרוק טופס 1312א קיים
        </p>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; max-width: 1000px; margin: 0 auto;">
            <!-- Digital Form Option -->
            <div class="form-option" onclick="selectFormType('digital')" style="background: white; color: #333; padding: 25px; border-radius: 12px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <div style="font-size: 48px; color: #28a745; margin-bottom: 15px;">
                    <i class="fas fa-laptop"></i>
                </div>
                <h3 style="margin-bottom: 10px; color: #28a745;">מילוי דיגיטלי</h3>
                <p style="margin-bottom: 15px; font-size: 14px; line-height: 1.5;">
                    מלא את הטופס באופן דיגיטלי עם ולידציה אוטומטית, השלמה חכמה וסריקת מסמכים
                </p>
                <div style="background: #e8f5e8; padding: 10px; border-radius: 6px; font-size: 12px;">
                    ✓ מילוי מהיר וקל<br>
                    ✓ ולידציה בזמן אמת<br>
                    ✓ סריקת מסמכים אוטומטית
                </div>
            </div>
            
            <!-- Scan Form Option -->
            <div class="form-option" onclick="selectFormType('scan')" style="background: white; color: #333; padding: 25px; border-radius: 12px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <div style="font-size: 48px; color: #007bff; margin-bottom: 15px;">
                    <i class="fas fa-scanner"></i>
                </div>
                <h3 style="margin-bottom: 10px; color: #007bff;">סריקת טופס 1312א</h3>
                <p style="margin-bottom: 15px; font-size: 14px; line-height: 1.5;">
                    סרוק טופס 1312א שכבר מולא ידנית והמערכת תזהה את הנתונים אוטומטית
                </p>
                <div style="background: #e8f4fd; padding: 10px; border-radius: 6px; font-size: 12px;">
                    ✓ סריקה מתקדמת OCR<br>
                    ✓ זיהוי אוטומטי של שדות<br>
                    ✓ עיבוד מהיר ומדויק
                </div>
            </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; max-width: 1000px; margin: 20px auto 0;">
            <!-- Existing Certificate Option -->
            <div class="form-option" onclick="selectFormType('certificate')" style="background: white; color: #333; padding: 25px; border-radius: 12px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <div style="font-size: 48px; color: #ffc107; margin-bottom: 15px;">
                    <i class="fas fa-certificate"></i>
                </div>
                <h3 style="margin-bottom: 10px; color: #ffc107;">אישור תושבות קיים</h3>
                <p style="margin-bottom: 15px; font-size: 14px; line-height: 1.5;">
                    יש לך כבר אישור תושבות? סרוק אותו והעבר את המסמכים הנדרשים לשליחה
                </p>
                <div style="background: #fff3cd; padding: 10px; border-radius: 6px; font-size: 12px;">
                    ✓ סריקת אישור קיים<br>
                    ✓ העברת מסמכים נדרשים<br>
                    ✓ שליחה ישירה לרשות המס
                </div>
            </div>

            <!-- Additional Documents Option -->
            <div class="form-option" onclick="selectFormType('additional')" style="background: white; color: #333; padding: 25px; border-radius: 12px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <div style="font-size: 48px; color: #6f42c1; margin-bottom: 15px;">
                    <i class="fas fa-file-medical"></i>
                </div>
                <h3 style="margin-bottom: 10px; color: #6f42c1;">מסמכים נוספים לזיכויים</h3>
                <p style="margin-bottom: 15px; font-size: 14px; line-height: 1.5;">
                    סרוק מסמכים נוספים לקבלת זיכויים במס: קופות גמל, אישורים רפואיים, תעודות עולה ועוד
                </p>
                <div style="background: #f3e8ff; padding: 10px; border-radius: 6px; font-size: 12px;">
                    ✓ אישורי קופות גמל<br>
                    ✓ אישורים רפואיים<br>
                    ✓ תעודות עולה וחיילים משוחררים
                </div>
            </div>
        </div>
        
        <div style="margin-top: 25px; font-size: 14px; opacity: 0.8;">
            <i class="fas fa-info-circle" style="margin-left: 5px;"></i>
            ניתן לשנות את הבחירה בכל עת
        </div>
    </div>

    <!-- COMPLETE FORM 1312 SYSTEM BACKUP -->
    <!-- This backup contains the full system structure -->
    <!-- To restore: copy content from Form_1312_Digital.html -->

    <div style="text-align: center; padding: 50px; background: #f8f9fa; margin: 20px; border-radius: 15px;">
        <i class="fas fa-save" style="font-size: 64px; color: #28a745; margin-bottom: 20px;"></i>
        <h2 style="color: #28a745; margin-bottom: 15px;">מערכת טופס 1312א - גיבוי מלא</h2>
        <p style="color: #6c757d; margin-bottom: 20px; line-height: 1.6;">
            הקובץ הזה מכיל גיבוי של המערכת המלאה שפיתחנו:<br>
            ✓ 4 אפשרויות מילוי טופס<br>
            ✓ מערכת OCR מתקדמת<br>
            ✓ סריקת מסמכים אוטומטית<br>
            ✓ ולידציה מלאה<br>
            ✓ עיצוב רספונסיבי<br>
            ✓ תמיכה בכל סוגי המסמכים
        </p>

        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h4 style="color: #155724; margin-bottom: 10px;">תכונות המערכת:</h4>
            <ul style="text-align: right; color: #155724; line-height: 1.8;">
                <li>מילוי דיגיטלי עם ולידציה בזמן אמת</li>
                <li>סריקת טופס 1312א קיים עם OCR</li>
                <li>העלאת אישור תושבות ומסמכים נדרשים</li>
                <li>סריקת מסמכים נוספים לזיכויים במס</li>
                <li>תמיכה בקופות גמל, אישורים רפואיים, תעודות עולה</li>
                <li>חישוב אוטומטי של זיכויים משוערים</li>
                <li>שליחה ישירה לרשות המס</li>
                <li>עיצוב מקצועי ורספונסיבי</li>
            </ul>
        </div>

        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <p style="color: #856404; margin: 0; font-weight: bold;">
                📁 הקובץ המלא נמצא ב: Form_1312_Digital.html
            </p>
        </div>

        <p style="color: #6c757d; font-size: 14px;">
            תאריך יצירה: ${new Date().toLocaleDateString('he-IL')}<br>
            גרסה: 1.0 - מערכת מלאה
        </p>
    </div>

    <script>
        console.log('=== טופס 1312א - מערכת מלאה ===');
        console.log('✓ 4 אפשרויות מילוי');
        console.log('✓ מערכת OCR מתקדמת');
        console.log('✓ סריקת מסמכים');
        console.log('✓ ולידציה מלאה');
        console.log('✓ עיצוב רספונסיבי');
        console.log('📁 קובץ מלא: Form_1312_Digital.html');
    </script>
</body>
</html>
