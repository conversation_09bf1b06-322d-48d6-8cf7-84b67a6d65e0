<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaxFree Pro - מערכת חישוב החזר מס ישראלית מקצועית</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .logo {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .tagline {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .progress-bar {
            height: 6px;
            background: #e0e0e0;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            transition: width 0.3s ease;
            width: 33.33%;
        }

        .step-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .step-title {
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .step-description {
            opacity: 0.9;
        }

        .step-content {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .required {
            color: #e74c3c;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            background: white;
            cursor: pointer;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e0e0e0;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .btn-container {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }

        .error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 5px;
            display: none;
        }

        .success-message {
            color: #27ae60;
            font-size: 14px;
            margin-top: 5px;
            display: none;
        }

        .step {
            display: none;
        }

        .step.active {
            display: block;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .feature-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 20px;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .feature-description {
            color: #666;
            line-height: 1.6;
        }

        .tax-tables {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e0e0e0;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .btn-container {
                flex-direction: column;
                gap: 15px;
            }

            .features {
                grid-template-columns: 1fr;
            }
        }

        .validation-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            display: none;
        }

        .form-group {
            position: relative;
        }

        .valid .validation-icon.success {
            display: block;
            color: #27ae60;
        }

        .invalid .validation-icon.error {
            display: block;
            color: #e74c3c;
        }

        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin: 20px 0;
        }

        .checkbox-input {
            width: 20px;
            height: 20px;
            margin: 0;
        }

        .checkbox-label {
            flex: 1;
            line-height: 1.5;
            cursor: pointer;
        }

        .legal-text {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-right: 4px solid #667eea;
        }

        .expandable {
            cursor: pointer;
            user-select: none;
        }

        .expandable:hover {
            background: #e9ecef;
        }

        .expandable-content {
            display: none;
            padding-top: 15px;
            border-top: 1px solid #e0e0e0;
            margin-top: 15px;
        }

        .expandable.expanded .expandable-content {
            display: block;
        }

        .file-upload {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload:hover {
            background: #f8f9fa;
            border-color: #5a6fd8;
        }

        .file-upload.dragover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .upload-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1.1rem;
            color: #333;
            margin-bottom: 10px;
        }

        .upload-subtext {
            color: #666;
            font-size: 0.9rem;
        }

        .file-list {
            margin-top: 20px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .file-icon {
            color: #667eea;
        }

        .remove-file {
            color: #e74c3c;
            cursor: pointer;
            padding: 5px;
        }

        .remove-file:hover {
            background: #ffebee;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <i class="fas fa-calculator"></i> TaxFree Pro
            </div>
            <div class="tagline">מערכת חישוב החזר מס ישראלית מקצועית</div>
        </div>

        <!-- Main Application -->
        <div class="main-card">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <!-- Step 1: Personal Information -->
            <div class="step active" id="step1">
                <div class="step-header">
                    <div class="step-title">
                        <i class="fas fa-user"></i> שלב 1: פרטים אישיים
                    </div>
                    <div class="step-description">אנא מלא את הפרטים האישיים שלך</div>
                </div>
                <div class="step-content">
                    <form id="personalForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">שם פרטי <span class="required">*</span></label>
                                <input type="text" class="form-input" id="firstName" required>
                                <i class="fas fa-check validation-icon success"></i>
                                <i class="fas fa-times validation-icon error"></i>
                                <div class="error-message">שדה חובה</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">שם משפחה <span class="required">*</span></label>
                                <input type="text" class="form-input" id="lastName" required>
                                <i class="fas fa-check validation-icon success"></i>
                                <i class="fas fa-times validation-icon error"></i>
                                <div class="error-message">שדה חובה</div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">מספר זהות <span class="required">*</span></label>
                                <input type="text" class="form-input" id="idNumber" required maxlength="9">
                                <i class="fas fa-check validation-icon success"></i>
                                <i class="fas fa-times validation-icon error"></i>
                                <div class="error-message">מספר זהות לא תקין</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">טלפון <span class="required">*</span></label>
                                <input type="tel" class="form-input" id="phone" required>
                                <i class="fas fa-check validation-icon success"></i>
                                <i class="fas fa-times validation-icon error"></i>
                                <div class="error-message">מספר טלפון לא תקין</div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">כתובת אימייל <span class="required">*</span></label>
                            <input type="email" class="form-input" id="email" required>
                            <i class="fas fa-check validation-icon success"></i>
                            <i class="fas fa-times validation-icon error"></i>
                            <div class="error-message">כתובת אימייל לא תקינה</div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">מצב משפחתי <span class="required">*</span></label>
                                <select class="form-select" id="maritalStatus" required>
                                    <option value="">בחר מצב משפחתי</option>
                                    <option value="single">רווק/ה</option>
                                    <option value="married">נשוי/ה</option>
                                    <option value="divorced">גרוש/ה</option>
                                    <option value="widowed">אלמן/ה</option>
                                </select>
                                <div class="error-message">יש לבחור מצב משפחתי</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">מספר ילדים עד גיל 18 <span class="required">*</span></label>
                                <input type="number" class="form-input" id="children" min="0" max="20" required>
                                <i class="fas fa-check validation-icon success"></i>
                                <i class="fas fa-times validation-icon error"></i>
                                <div class="error-message">שדה חובה</div>
                            </div>
                        </div>
                    </form>

                    <div style="background: #e8f4fd; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px; margin: 20px 0;">
                        <div style="display: flex; align-items: center; margin-bottom: 10px;">
                            <i class="fas fa-info-circle" style="color: #0c5460; margin-left: 8px; font-size: 18px;"></i>
                            <strong style="color: #0c5460;">השלב הבא</strong>
                        </div>
                        <p style="color: #0c5460; margin: 0; line-height: 1.5;">
                            לאחר מילוי הפרטים האישיים, תועבר לעמוד תנאי השימוש והסכמות
                            שכולל את טופס 8832 (יפוי כח) לטיפול במס הכנסה.
                        </p>
                    </div>

                    <div class="btn-container">
                        <button class="btn btn-secondary" onclick="testRedirect()" style="margin-left: 10px;">
                            בדיקה ישירה <i class="fas fa-bug"></i>
                        </button>
                        <button class="btn btn-primary" onclick="nextStep(1)">
                            המשך לתנאי השימוש <i class="fas fa-arrow-left"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Step 2: Terms of Service -->
            <div class="step" id="step2">
                <div class="step-header">
                    <div class="step-title">
                        <i class="fas fa-file-contract"></i> שלב 2: תנאי השירות
                    </div>
                    <div class="step-description">אנא קרא בעיון את תנאי השירות ואשר את הסכמתך</div>
                </div>
                <div class="step-content">
                    <div class="legal-text">
                        <h3><i class="fas fa-info-circle"></i> תנאי השירות - TaxFree Pro</h3>

                        <div class="expandable" onclick="toggleExpand(this)">
                            <h4><i class="fas fa-chevron-down"></i> 1. תמחור מבוסס הצלחה</h4>
                            <div class="expandable-content">
                                <p><strong>שיעור עמלה:</strong> 15% מסכום החזר המס בפועל</p>
                                <p><strong>תשלום:</strong> רק במקרה של קבלת החזר מס חיובי</p>
                                <p><strong>אין החזר - אין תשלום:</strong> במקרה שלא מתקבל החזר מס, לא יחויב הלקוח בתשלום כלשהו</p>
                                <p><strong>דוגמה:</strong> החזר מס של 10,000 ₪ = עמלה של 1,500 ₪</p>
                            </div>
                        </div>

                        <div class="expandable" onclick="toggleExpand(this)">
                            <h4><i class="fas fa-chevron-down"></i> 2. ייפוי כוח (טופס 8832)</h4>
                            <div class="expandable-content">
                                <p><strong>הסמכה:</strong> הלקוח מסמיך את TaxFree Pro לפעול מטעמו מול רשות המסים</p>
                                <p><strong>היקף הייפוי:</strong> הגשת בקשה להחזר מס, מעקב אחר הטיפול, קבלת מידע</p>
                                <p><strong>משך הייפוי:</strong> עד לסיום הטיפול בבקשה או ביטול על ידי הלקוח</p>
                                <p><strong>ביטול:</strong> ניתן לבטל את ייפוי הכוח בכל עת בהודעה בכתב</p>
                            </div>
                        </div>

                        <div class="expandable" onclick="toggleExpand(this)">
                            <h4><i class="fas fa-chevron-down"></i> 3. הגנת פרטיות ואבטחת מידע</h4>
                            <div class="expandable-content">
                                <p><strong>הצפנה:</strong> כל המידע מוצפן ברמה הגבוהה ביותר</p>
                                <p><strong>אחסון:</strong> המידע נשמר בשרתים מאובטחים בישראל בלבד</p>
                                <p><strong>גישה:</strong> רק אנשי מקצוע מורשים יכולים לגשת למידע</p>
                                <p><strong>מחיקה:</strong> המידע נמחק לאחר סיום הטיפול (למעט חובות חוקיות)</p>
                            </div>
                        </div>

                        <div class="expandable" onclick="toggleExpand(this)">
                            <h4><i class="fas fa-chevron-down"></i> 4. ציות רגולטורי</h4>
                            <div class="expandable-content">
                                <p><strong>רישוי:</strong> החברה פועלת ברישיון מלא של רשות המסים</p>
                                <p><strong>ביטוח:</strong> ביטוח אחריות מקצועית בסכום של 5 מיליון ₪</p>
                                <p><strong>פיקוח:</strong> כפיפות לפיקוח רשות המסים ולשכת רואי החשבון</p>
                                <p><strong>דיווח:</strong> דיווח שנתי לרשויות הרלוונטיות</p>
                            </div>
                        </div>

                        <div class="expandable" onclick="toggleExpand(this)">
                            <h4><i class="fas fa-chevron-down"></i> 5. זמני תשלום</h4>
                            <div class="expandable-content">
                                <p><strong>מועד תשלום:</strong> תוך 30 יום מקבלת החזר המס בפועל</p>
                                <p><strong>אופן תשלום:</strong> העברה בנקאית או המחאה</p>
                                <p><strong>חשבונית:</strong> תישלח חשבונית מס כחוק</p>
                                <p><strong>פיגור:</strong> ריבית פיגורים לפי חוק פסיקת ריבית והצמדה</p>
                            </div>
                        </div>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" class="checkbox-input" id="agreeTerms" required>
                        <label class="checkbox-label" for="agreeTerms">
                            <strong>קראתי את התנאים והבנתי ומאשר אותם</strong><br>
                            <small>אני מאשר כי קראתי בעיון את כל תנאי השירות לעיל, הבנתי את משמעותם המשפטית והכלכלית, ואני מסכים לכל התנאים ללא סייג. אני מבין כי הסכמתי זו מהווה התחייבות משפטית מלאה.</small>
                        </label>
                    </div>

                    <div class="btn-container">
                        <button class="btn btn-secondary" onclick="prevStep(2)">
                            <i class="fas fa-arrow-right"></i> חזור לשלב הקודם
                        </button>
                        <button class="btn btn-primary" onclick="nextStep(2)">
                            המשך לחישוב המס <i class="fas fa-arrow-left"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Step 3: Tax Calculation -->
            <div class="step" id="step3">
                <div class="step-header">
                    <div class="step-title">
                        <i class="fas fa-calculator"></i> שלב 3: חישוב החזר מס
                    </div>
                    <div class="step-description">העלה את המסמכים שלך וקבל חישוב מדויק</div>
                </div>
                <div class="step-content">
                    <div class="file-upload" id="fileUpload">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">גרור קבצים לכאן או לחץ לבחירה</div>
                        <div class="upload-subtext">
                            נתמך: PDF, JPG, PNG, DOC, DOCX (עד 10MB לקובץ)
                        </div>
                        <input type="file" id="fileInput" multiple accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" style="display: none;">
                    </div>

                    <div class="file-list" id="fileList"></div>

                    <div class="tax-tables">
                        <h3><i class="fas fa-table"></i> טבלאות מס ישראליות 2025</h3>

                        <h4>מדרגות מס הכנסה</h4>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>מדרגה</th>
                                    <th>הכנסה שנתית (₪)</th>
                                    <th>שיעור מס</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td>1</td><td>עד 87,360</td><td>10%</td></tr>
                                <tr><td>2</td><td>87,361-125,280</td><td>14%</td></tr>
                                <tr><td>3</td><td>125,281-201,120</td><td>20%</td></tr>
                                <tr><td>4</td><td>201,121-279,480</td><td>31%</td></tr>
                                <tr><td>5</td><td>279,481-585,600</td><td>35%</td></tr>
                                <tr><td>6</td><td>585,601-749,280</td><td>47%</td></tr>
                                <tr><td>7</td><td>מעל 749,280</td><td>50%</td></tr>
                            </tbody>
                        </table>

                        <h4>נקודות זיכוי עיקריות</h4>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>סוג זיכוי</th>
                                    <th>נקודות חודשיות</th>
                                    <th>ערך שנתי (₪)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td>זיכוי בסיסי</td><td>2.25</td><td>2,772</td></tr>
                                <tr><td>הורה לילד עד 5</td><td>2.5</td><td>3,080</td></tr>
                                <tr><td>הורה לילד 6-17</td><td>1.5</td><td>1,848</td></tr>
                                <tr><td>אקדמאי</td><td>0.5</td><td>616</td></tr>
                                <tr><td>משוחרר צה"ל</td><td>1.5</td><td>1,848</td></tr>
                            </tbody>
                        </table>

                        <h4>פטורים ממס עיקריים</h4>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>סוג פטור</th>
                                    <th>סכום/שיעור</th>
                                    <th>תנאים</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td>שכ"ד בסיסי</td><td>5,196 ₪</td><td>לכל משכיר</td></tr>
                                <tr><td>ריבית משכנתא</td><td>עד 57,720 ₪</td><td>דירת מגורים ראשית</td></tr>
                                <tr><td>מלגות לימודים</td><td>עד 98,000 ₪</td><td>ממוסדות מוכרים</td></tr>
                                <tr><td>תרומות</td><td>זיכוי 35%</td><td>עד 30% מההכנסה</td></tr>
                                <tr><td>קרן השתלמות</td><td>עד 13,203 ₪</td><td>4.5% מההכנסה</td></tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="btn-container">
                        <button class="btn btn-secondary" onclick="prevStep(3)">
                            <i class="fas fa-arrow-right"></i> חזור לשלב הקודם
                        </button>
                        <button class="btn btn-primary" onclick="calculateTax()">
                            חשב החזר מס <i class="fas fa-calculator"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="feature-title">אבטחה מתקדמת</div>
                <div class="feature-description">
                    הצפנה ברמה בנקאית, אחסון מאובטח בישראל, ומחיקת נתונים אוטומטית
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="feature-title">OCR מתקדם</div>
                <div class="feature-description">
                    זיהוי אוטומטי של מסמכי מס, טפסים 106, 161, 867, ואישורי פנסיה
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="feature-title">חישוב מדויק</div>
                <div class="feature-description">
                    אלגוריתמים מתקדמים המבוססים על חוקי מס הכנסה הישראליים העדכניים
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <div class="feature-title">תמיכה מקצועית</div>
                <div class="feature-description">
                    צוות רואי חשבון מוסמכים זמין לייעוץ ותמיכה לאורך כל התהליך
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentStep = 1;
        let uploadedFiles = [];

        // Step navigation
        function nextStep(step) {
            console.log('nextStep called with step:', step);

            // If completing step 1 (personal details), validate and redirect to terms page
            if (step === 1) {
                console.log('Validating personal info...');

                if (validatePersonalInfo()) {
                    console.log('Validation passed, saving data...');

                    // Save form data to localStorage for later use
                    saveFormData();

                    // Show loading message
                    showLoadingMessage();

                    // Redirect to terms and conditions page after short delay
                    setTimeout(() => {
                        console.log('Redirecting to index.html...');
                        window.location.href = 'index.html';
                    }, 2000);
                } else {
                    console.log('Validation failed');
                    alert('אנא מלא את כל השדות הנדרשים בצורה תקינה');
                }
                return;
            }

            // For other steps, continue with normal flow
            if (validateStep(step)) {
                currentStep++;
                updateProgress();
                showStep(currentStep);
            }
        }

        function showLoadingMessage() {
            console.log('Showing loading message...');

            // Create loading overlay
            const overlay = document.createElement('div');
            overlay.id = 'loadingOverlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                color: white;
                font-size: 18px;
                font-weight: bold;
            `;
            overlay.innerHTML = `
                <div style="text-align: center;">
                    <div style="margin-bottom: 20px;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 48px; color: #007bff;"></i>
                    </div>
                    <div>מעביר לעמוד תנאי השימוש...</div>
                    <div style="font-size: 14px; margin-top: 10px; opacity: 0.8;">
                        טוען טופס 8832 ותנאי השירות
                    </div>
                </div>
            `;
            document.body.appendChild(overlay);
            console.log('Loading overlay added to page');
        }

        // Test function for direct redirect
        function testRedirect() {
            console.log('Test redirect function called');

            // Create test data
            const testData = {
                firstName: 'ישראל',
                lastName: 'ישראלי',
                fullName: 'ישראל ישראלי',
                idNumber: '*********',
                phone: '03-1234567',
                email: '<EMAIL>',
                maritalStatus: 'married',
                children: '2',
                timestamp: new Date().toISOString()
            };

            // Save test data
            localStorage.setItem('taxfree_personal_data', JSON.stringify(testData));
            console.log('Test data saved:', testData);

            // Show loading and redirect
            showLoadingMessage();

            setTimeout(() => {
                console.log('Redirecting to index.html (test)...');
                window.location.href = 'index.html';
            }, 1000);
        }

        // Save form data to localStorage
        function saveFormData() {
            const firstName = document.getElementById('firstName').value.trim();
            const lastName = document.getElementById('lastName').value.trim();
            const fullName = `${firstName} ${lastName}`;

            const formData = {
                firstName: firstName,
                lastName: lastName,
                fullName: fullName,
                idNumber: document.getElementById('idNumber').value.trim(),
                phone: document.getElementById('phone').value.trim(),
                email: document.getElementById('email').value.trim(),
                maritalStatus: document.getElementById('maritalStatus').value,
                children: document.getElementById('children').value.trim(),
                timestamp: new Date().toISOString()
            };

            localStorage.setItem('taxfree_personal_data', JSON.stringify(formData));
            console.log('Personal data saved:', formData);

            // Show success message
            alert('הפרטים נשמרו בהצלחה!\nעכשיו תועבר לעמוד תנאי השימוש.');
        }

        function prevStep(step) {
            currentStep--;
            updateProgress();
            showStep(currentStep);
        }

        function showStep(step) {
            document.querySelectorAll('.step').forEach(s => s.classList.remove('active'));
            document.getElementById(`step${step}`).classList.add('active');
        }

        function updateProgress() {
            const progress = (currentStep / 3) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // Validation functions
        function validateStep(step) {
            switch(step) {
                case 1:
                    return validatePersonalInfo();
                case 2:
                    return validateTerms();
                default:
                    return true;
            }
        }

        function validatePersonalInfo() {
            let isValid = true;

            // Validate individual fields
            const firstName = document.getElementById('firstName');
            const lastName = document.getElementById('lastName');
            const idNumber = document.getElementById('idNumber');
            const phone = document.getElementById('phone');
            const email = document.getElementById('email');
            const maritalStatus = document.getElementById('maritalStatus');
            const children = document.getElementById('children');

            // Check first name
            if (!firstName.value.trim()) {
                showError(firstName, 'שדה חובה');
                isValid = false;
            } else {
                showSuccess(firstName);
            }

            // Check last name
            if (!lastName.value.trim()) {
                showError(lastName, 'שדה חובה');
                isValid = false;
            } else {
                showSuccess(lastName);
            }

            // Check ID number
            if (!idNumber.value.trim()) {
                showError(idNumber, 'שדה חובה');
                isValid = false;
            } else if (!validateIsraeliID(idNumber.value.trim())) {
                showError(idNumber, 'מספר זהות לא תקין');
                isValid = false;
            } else {
                showSuccess(idNumber);
            }

            // Check phone
            if (!phone.value.trim()) {
                showError(phone, 'שדה חובה');
                isValid = false;
            } else if (!validatePhone(phone.value.trim())) {
                showError(phone, 'מספר טלפון לא תקין');
                isValid = false;
            } else {
                showSuccess(phone);
            }

            // Check email
            if (!email.value.trim()) {
                showError(email, 'שדה חובה');
                isValid = false;
            } else if (!validateEmail(email.value.trim())) {
                showError(email, 'כתובת אימייל לא תקינה');
                isValid = false;
            } else {
                showSuccess(email);
            }

            // Check marital status
            if (!maritalStatus.value) {
                showError(maritalStatus, 'יש לבחור מצב משפחתי');
                isValid = false;
            } else {
                showSuccess(maritalStatus);
            }

            // Check children
            if (!children.value.trim()) {
                showError(children, 'שדה חובה');
                isValid = false;
            } else {
                showSuccess(children);
            }

            return isValid;
        }

        function validateTerms() {
            const agreeTerms = document.getElementById('agreeTerms');
            if (!agreeTerms.checked) {
                alert('יש לאשר את תנאי השירות כדי להמשיך');
                return false;
            }
            return true;
        }

        function validateIsraeliID(id) {
            if (id.length !== 9 || !/^\d+$/.test(id)) return false;

            let sum = 0;
            for (let i = 0; i < 9; i++) {
                let digit = parseInt(id[i]);
                if (i % 2 === 1) {
                    digit *= 2;
                    if (digit > 9) digit -= 9;
                }
                sum += digit;
            }
            return sum % 10 === 0;
        }

        function validateEmail(email) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        }

        function validatePhone(phone) {
            return /^[\d\-\+\(\)\s]+$/.test(phone) && phone.replace(/\D/g, '').length >= 9;
        }

        function showError(element, message) {
            const group = element.closest('.form-group');
            group.classList.remove('valid');
            group.classList.add('invalid');
            const errorMsg = group.querySelector('.error-message');
            if (errorMsg) {
                errorMsg.textContent = message;
                errorMsg.style.display = 'block';
            }
        }

        function showSuccess(element) {
            const group = element.closest('.form-group');
            group.classList.remove('invalid');
            group.classList.add('valid');
            const errorMsg = group.querySelector('.error-message');
            if (errorMsg) {
                errorMsg.style.display = 'none';
            }
        }

        // File upload functionality
        function initFileUpload() {
            const fileUpload = document.getElementById('fileUpload');
            const fileInput = document.getElementById('fileInput');

            fileUpload.addEventListener('click', () => fileInput.click());
            fileUpload.addEventListener('dragover', handleDragOver);
            fileUpload.addEventListener('drop', handleDrop);
            fileInput.addEventListener('change', handleFileSelect);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            processFiles(files);
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            processFiles(files);
        }

        function processFiles(files) {
            files.forEach(file => {
                if (validateFile(file)) {
                    uploadedFiles.push(file);
                    addFileToList(file);
                }
            });
        }

        function validateFile(file) {
            const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
            const maxSize = 10 * 1024 * 1024; // 10MB

            if (!allowedTypes.includes(file.type)) {
                alert('סוג קובץ לא נתמך: ' + file.name);
                return false;
            }

            if (file.size > maxSize) {
                alert('קובץ גדול מדי: ' + file.name);
                return false;
            }

            return true;
        }

        function addFileToList(file) {
            const fileList = document.getElementById('fileList');
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <i class="fas fa-file file-icon"></i>
                    <span>${file.name}</span>
                    <small>(${formatFileSize(file.size)})</small>
                </div>
                <i class="fas fa-times remove-file" onclick="removeFile('${file.name}')"></i>
            `;
            fileList.appendChild(fileItem);
        }

        function removeFile(fileName) {
            uploadedFiles = uploadedFiles.filter(file => file.name !== fileName);
            updateFileList();
        }

        function updateFileList() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            uploadedFiles.forEach(file => addFileToList(file));
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Expandable sections
        function toggleExpand(element) {
            element.classList.toggle('expanded');
            const icon = element.querySelector('i');
            if (element.classList.contains('expanded')) {
                icon.className = 'fas fa-chevron-up';
            } else {
                icon.className = 'fas fa-chevron-down';
            }
        }

        // Tax calculation
        function calculateTax() {
            // Show loading state
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> מחשב...';
            btn.disabled = true;

            // Simulate calculation
            setTimeout(() => {
                // Reset button
                btn.innerHTML = originalText;
                btn.disabled = false;

                // Show results
                showTaxResults();
            }, 3000);
        }

        function showTaxResults() {
            alert('חישוב הושלם! החזר מס צפוי: 15,750 ₪\n\nהדוח המלא נשלח לאימייל שלך.');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded - TaxFree Pro Personal Details');
            console.log('Available functions:', {
                nextStep: typeof nextStep,
                validatePersonalInfo: typeof validatePersonalInfo,
                saveFormData: typeof saveFormData,
                testRedirect: typeof testRedirect
            });

            initFileUpload();

            // Add real-time validation
            document.querySelectorAll('.form-input').forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.value.trim()) {
                        showSuccess(this);
                    }
                });
            });
        });
    </script>
</body>
</html>